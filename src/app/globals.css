@import 'tailwindcss';

:root {
  --background: linear-gradient(to top, #edf8ff 0%, #c3c3f5 100%);
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-line-seed-sans), var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-line-seed-sans), Arial, Helvetica, sans-serif;
}

/* Mobile chat view - hide footer/bottom navigation */
@media (max-width: 767px) {
  body.mobile-chat-view header {
    display: none !important;
  }
}
